'use client';

import React from 'react';
import {
  ReadingProgress,
  TableOfContents,
  ArticleInteractions,
  FocusMode,
} from '@/components/blog';
import { BlogPost, TableOfContentsItem } from '@/types';

interface BlogPostClientProps {
  post: BlogPost;
  tableOfContents: TableOfContentsItem[];
  siteUrl: string;
  locale: string;
}

export function BlogPostClient({ 
  post, 
  tableOfContents 
}: BlogPostClientProps) {
  return (
    <>
      {/* 阅读进度指示器 */}
      <ReadingProgress target="article" variant="bar" />

      {/* 左侧目录 - 仅桌面端显示，Medium风格 */}
      {tableOfContents.length > 0 && (
        <TableOfContents
          items={tableOfContents}
          variant="floating"
          className="hidden xl:block fixed left-6 top-1/2 transform -translate-y-1/2 z-10 max-w-xs w-64"
        />
      )}

      {/* 右侧浮动工具栏 - 仅保留核心功能 */}
      <div className="fixed right-6 top-1/2 transform -translate-y-1/2 z-40 hidden lg:block">
        <ArticleInteractions
          postId={post.id}
          initialLikes={post.likeCount}
          initialViews={post.viewCount}
          initialComments={post.commentCount}
          variant="floating"
        />
      </div>

      {/* 专注模式控制 - 右下角 */}
      <FocusMode className="fixed right-6 bottom-6 z-50" />

    </>
  );
}
